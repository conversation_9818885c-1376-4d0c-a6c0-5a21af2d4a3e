from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.messages import HumanMessage
from IPython.display import Image, display, Markdown


# Initialize model for image generation
llm = ChatGoogleGenerativeAI(model="models/gemini-2.0-flash-exp-image-generation")
 
message = {
    "role": "user",
    "content": "Generate an image of a cat wearing a hat.",
}
 
response = llm.invoke(
    [message],
    generation_config=dict(response_modalities=["TEXT", "IMAGE"]),
)
 

# Display the generated image
import base64


image_base64 = response.content[1].get("image_url").get("url").split(",")[-1]
image_data = base64.b64decode(image_base64)
display(Image(data=image_data, width=300))

llm = ChatGoogleGenerativeAI(
    model="gemini-2.0-flash",
    temperature=0,
)
 
prompt = ChatPromptTemplate.from_messages([
    ("system", "You are a helpful assistant that translates {input_language} to {output_language}."),
    ("human", "{input}"),
])
 
chain = prompt | llm
result = chain.invoke({
    "input_language": "English",
    "output_language": "urdu",
    "input": "I love programming.",
})
print(result.content)

llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash")
 
# Using an image URL
message_url = HumanMessage(
    content=[
        {"type": "text", "text": "Describe this image."},
        {"type": "image_url", "image_url": "https://picsum.photos/seed/picsum/200/300"},
    ]
)
result_url = llm.invoke([message_url])
print(result_url.content)
 
# Using a local image
# local_image_path = "../assets/react.png"
# with open(local_image_path, "rb") as image_file:
#     encoded_image = base64.b64encode(image_file.read()).decode('utf-8')
 
# message_local = HumanMessage(
#     content=[
#         {"type": "text", "text": "Describe this image."},
#         {"type": "image_url", "image_url": f"data:image/png;base64,{encoded_image}"}
#     ]
# )
# result_local = llm.invoke([message_local])
# print(result_local.content)